<?php
/**
 * XAMPP Virtual Host Setup Script
 * This script helps configure XAMPP to serve your project directly
 */

$project_path = 'E:/build/httpdocs/baffletrain/autocadlt/autobooks';
$xampp_path = 'E:/tools/xampp';
$vhost_conf = $xampp_path . '/apache/conf/extra/httpd-vhosts.conf';
$httpd_conf = $xampp_path . '/apache/conf/httpd.conf';

echo "XAMPP Virtual Host Setup\n";
echo "========================\n\n";

// Check if files exist
if (!file_exists($vhost_conf)) {
    echo "❌ XAMPP virtual hosts config not found: $vhost_conf\n";
    exit(1);
}

if (!file_exists($httpd_conf)) {
    echo "❌ XAMPP main config not found: $httpd_conf\n";
    exit(1);
}

echo "✓ XAMPP installation found\n";
echo "✓ Project path: $project_path\n\n";

// Read current vhost configuration
$vhost_content = file_get_contents($vhost_conf);

// Check if our virtual host already exists
if (strpos($vhost_content, 'autobooks.local') !== false) {
    echo "⚠️  Virtual host 'autobooks.local' already exists\n";
} else {
    echo "Adding virtual host configuration...\n";
    
    $vhost_config = "\n\n# Autobooks Local Development\n";
    $vhost_config .= "<VirtualHost *:80>\n";
    $vhost_config .= "    DocumentRoot \"$project_path\"\n";
    $vhost_config .= "    ServerName autobooks.local\n";
    $vhost_config .= "    ServerAlias www.autobooks.local\n";
    $vhost_config .= "    <Directory \"$project_path\">\n";
    $vhost_config .= "        AllowOverride All\n";
    $vhost_config .= "        Require all granted\n";
    $vhost_config .= "    </Directory>\n";
    $vhost_config .= "    ErrorLog \"logs/autobooks_error.log\"\n";
    $vhost_config .= "    CustomLog \"logs/autobooks_access.log\" common\n";
    $vhost_config .= "</VirtualHost>\n";
    
    // Append to vhost file
    file_put_contents($vhost_conf, $vhost_config, FILE_APPEND);
    echo "✓ Added virtual host configuration\n";
}

// Check if virtual hosts are enabled in main config
$httpd_content = file_get_contents($httpd_conf);
if (strpos($httpd_content, '#Include conf/extra/httpd-vhosts.conf') !== false) {
    echo "Enabling virtual hosts in main configuration...\n";
    $httpd_content = str_replace(
        '#Include conf/extra/httpd-vhosts.conf',
        'Include conf/extra/httpd-vhosts.conf',
        $httpd_content
    );
    file_put_contents($httpd_conf, $httpd_content);
    echo "✓ Enabled virtual hosts\n";
} else {
    echo "✓ Virtual hosts already enabled\n";
}

// Create hosts file entry instructions
echo "\n=== Next Steps ===\n";
echo "1. Add this line to your Windows hosts file:\n";
echo "   127.0.0.1    autobooks.local\n\n";
echo "   Hosts file location: C:\\Windows\\System32\\drivers\\etc\\hosts\n";
echo "   (You'll need to run Notepad as Administrator to edit it)\n\n";

echo "2. Restart Apache in XAMPP Control Panel\n\n";

echo "3. Access your application at:\n";
echo "   http://autobooks.local/\n";
echo "   http://autobooks.local/web_test_db_config.php\n\n";

echo "=== Alternative Quick Access ===\n";
echo "For immediate testing without virtual host setup:\n";
echo "http://localhost/baffletrain/autocadlt/autobooks/web_test_db_config.php\n\n";

echo "=== Manual Hosts File Edit ===\n";
echo "If you want to set up the virtual host:\n";
echo "1. Open Command Prompt as Administrator\n";
echo "2. Run: notepad C:\\Windows\\System32\\drivers\\etc\\hosts\n";
echo "3. Add this line at the end: 127.0.0.1    autobooks.local\n";
echo "4. Save and close\n";
echo "5. Restart Apache\n\n";

echo "Setup complete!\n";
?>
