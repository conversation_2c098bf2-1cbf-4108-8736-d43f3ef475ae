<?php
// Test script to debug autoloader issues
define('API_RUN', true); // Enable debug output

// Set up basic paths
$path = ['fs_app_root' => __DIR__ . '/'];
require_once($path['fs_app_root'] . "system/functions/functions.php");
require_once($path['fs_app_root'] . "system/functions/path_utils.php");
require_once($path['fs_app_root'] . "system/paths.php");

$path = build_paths($path);
build_constants($path);

// Load autoloader
require_once($path['fs_app_root'] . "system/autoloader.php");

echo "Testing autoloader...\n";
echo "FS_SYS_CLASSES: " . FS_SYS_CLASSES . "\n";

// Test loading the data_importer class
echo "Attempting to load system\\data_importer...\n";

try {
    $class = new system\data_importer();
    echo "SUCCESS: Class loaded successfully!\n";
} catch (Error $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "EXCEPTION: " . $e->getMessage() . "\n";
}

// Test the static method call
echo "Testing static method call...\n";
try {
    $result = system\data_importer::process_json_data([], [], [], true, "test");
    echo "SUCCESS: Static method call worked!\n";
} catch (Error $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "EXCEPTION: " . $e->getMessage() . "\n";
}
