<?php
/**
 * Local Database Setup Script
 * This script sets up your local development database
 */

// Configuration
$local_config = [
    'host' => '127.0.0.1',
    'port' => '3306',
    'username' => 'root',
    'password' => '', // XAMPP default is empty password
    'database' => 'autobooks_local'
];

$remote_config = [
    'host' => 'your-remote-host.com', // Replace with your web server host
    'username' => 'wwwcadservicescouk', // Your remote username
    'password' => 'S96#1kvYuCGE', // Your remote password
    'database' => 'wwwcadservicescouk' // Your remote database
];

echo "=== Local Database Setup ===\n";

try {
    // Connect to local MySQL (without database first)
    $local_pdo = new PDO(
        "mysql:host={$local_config['host']};port={$local_config['port']};charset=utf8",
        $local_config['username'],
        $local_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "✓ Connected to local MySQL server\n";
    
    // Create local database
    $local_pdo->exec("CREATE DATABASE IF NOT EXISTS `{$local_config['database']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✓ Created local database: {$local_config['database']}\n";
    
    // Create local user (optional, for security)
    $local_pdo->exec("CREATE USER IF NOT EXISTS 'autobooks'@'localhost' IDENTIFIED BY 'local_password'");
    $local_pdo->exec("GRANT ALL PRIVILEGES ON `{$local_config['database']}`.* TO 'autobooks'@'localhost'");
    $local_pdo->exec("FLUSH PRIVILEGES");
    echo "✓ Created local user: autobooks\n";
    
    // Connect to the specific database
    $local_db = new PDO(
        "mysql:host={$local_config['host']};port={$local_config['port']};dbname={$local_config['database']};charset=utf8",
        $local_config['username'],
        $local_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        ]
    );
    
    echo "✓ Connected to local database\n";
    
    // Create basic tables (based on your application structure)
    $tables = [
        'autobooks_notifications' => "
            CREATE TABLE IF NOT EXISTS `autobooks_notifications` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `type` varchar(50) NOT NULL,
                `title` varchar(255) NOT NULL,
                `message` text NOT NULL,
                `link` varchar(255) DEFAULT NULL,
                `is_read` tinyint(1) NOT NULL DEFAULT 0,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `read_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`),
                KEY `is_read` (`is_read`),
                KEY `created_at` (`created_at`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ",
        'autobooks_notification_preferences' => "
            CREATE TABLE IF NOT EXISTS `autobooks_notification_preferences` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `type` varchar(50) NOT NULL,
                `enabled` tinyint(1) NOT NULL DEFAULT 1,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `user_type` (`user_id`, `type`),
                KEY `user_id` (`user_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ",
        'autobooks_users' => "
            CREATE TABLE IF NOT EXISTS `autobooks_users` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `username` varchar(50) NOT NULL,
                `email` varchar(100) NOT NULL,
                `password` varchar(255) NOT NULL,
                `role` varchar(20) NOT NULL DEFAULT 'user',
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `username` (`username`),
                UNIQUE KEY `email` (`email`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ",
        'autodesk_storage' => "
            CREATE TABLE IF NOT EXISTS `autodesk_storage` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `autodesk_storage_key` varchar(255) NOT NULL,
                `autodesk_storage_data` longtext,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `autodesk_storage_key` (`autodesk_storage_key`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        "
    ];
    
    foreach ($tables as $table_name => $sql) {
        $local_db->exec($sql);
        echo "✓ Created table: $table_name\n";
    }
    
    echo "\n=== Setup Complete! ===\n";
    echo "Local database is ready at:\n";
    echo "Host: {$local_config['host']}\n";
    echo "Database: {$local_config['database']}\n";
    echo "Username: {$local_config['username']}\n";
    echo "Password: {$local_config['password']}\n";
    echo "\nNext steps:\n";
    echo "1. Update your local config to use this database\n";
    echo "2. Run the sync script to import data from remote\n";
    echo "3. Access phpMyAdmin at: http://localhost/phpmyadmin\n";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "\nMake sure XAMPP MySQL is running!\n";
    echo "Start it from the XAMPP Control Panel.\n";
}
?>
