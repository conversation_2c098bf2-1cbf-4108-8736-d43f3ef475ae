<?php
/**
 * Test Database Configuration
 */

echo "Testing Database Configuration...\n";
echo "================================\n\n";

// Include the configuration
require_once 'system/db_config.php';

echo "Environment Detection:\n";
echo "  Is Local: " . ($is_local ? 'YES' : 'NO') . "\n";
echo "  PHP SAPI: " . php_sapi_name() . "\n";
echo "  SERVER_NAME: " . ($_SERVER['SERVER_NAME'] ?? 'Not Set') . "\n";
echo "  SERVER_ADDR: " . ($_SERVER['SERVER_ADDR'] ?? 'Not Set') . "\n\n";

echo "Database Configuration:\n";
echo "  Server: $db_server\n";
echo "  Database: $db_database\n";
echo "  Username: $db_username\n";
echo "  Password: " . (empty($db_password) ? '(empty)' : '***') . "\n\n";

// Test the connection
try {
    $dsn = "mysql:host=$db_server;dbname=$db_database;charset=utf8";
    $pdo = new PDO($dsn, $db_username, $db_password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ]);
    
    $version = $pdo->query('SELECT VERSION()')->fetchColumn();
    echo "Connection Test:\n";
    echo "  ✓ Connection successful\n";
    echo "  ✓ Database version: $version\n";
    
    // Test if we can query a table
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "  ✓ Found " . count($tables) . " tables\n";
    
    if (count($tables) > 0) {
        echo "  Tables: " . implode(', ', array_slice($tables, 0, 5)) . 
             (count($tables) > 5 ? '...' : '') . "\n";
    }
    
} catch (PDOException $e) {
    echo "Connection Test:\n";
    echo "  ❌ Connection failed: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
