<?php
/**
 * Path utility functions
 */

/**
 * Normalize a path by removing double slashes and trimming
 * 
 * @param string $path Path to normalize
 * @return string Normalized path
 */
function normalize_path(string $path): string {
    // Replace multiple slashes with a single slash
    while (strpos($path, '//') !== false) {
        $path = str_replace('//', '/', $path);
    }
    
    // Trim slashes from beginning and end
    return trim(trim($path), '/');
}

/**
 * Join path segments with proper slash handling
 * 
 * @param string ...$segments Path segments to join
 * @return string Joined path
 */
function join_paths(string ...$segments): string {
    $path = implode('/', array_map(function($segment) {
        return trim(trim($segment), '/');
    }, $segments));
    
    return normalize_path($path);
}

/**
 * Build a filesystem path with leading slash
 * 
 * @param string $path Path to format
 * @return string Formatted filesystem path
 */
function fs_path(string $path): string {
    return '/' . normalize_path($path);
}

/**
 * Build a web path with leading slash
 * 
 * @param string $path Path to format
 * @return string Formatted web path
 */
function web_path(string $path): string {
    return '/' . normalize_path($path) . '/';
}

/**
 * Replace placeholders in a path with actual values
 * 
 * @param string $path Path with placeholders
 * @param array $replacements Key-value pairs for replacements
 * @return string Path with replacements
 */
function replace_path_placeholders(string $path, array $replacements): string {
    foreach ($replacements as $key => $value) {
        $path = str_replace('{' . $key . '}', $value, $path);
    }
    return $path;
}