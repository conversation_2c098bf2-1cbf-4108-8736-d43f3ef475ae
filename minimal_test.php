<?php
// Minimal test to check if the autoloader can find the data_importer class
define('API_RUN', false); // Disable debug output to avoid print_rr calls

// Define the constants manually for testing
define('FS_SYS_CLASSES', __DIR__ . '/system/classes/');
define('FS_CLASSES', __DIR__ . '/resources/classes/');
define('FS_API', __DIR__ . '/api/');
define('FS_COMPONENTS', __DIR__ . '/resources/components/');
define('FS_FUNCTIONS', __DIR__ . '/resources/functions/');
define('FS_VIEWS', __DIR__ . '/resources/views/');
define('FS_SYS_FUNCTIONS', __DIR__ . '/system/functions/');

// Load the autoloader
require_once(__DIR__ . "/system/autoloader.php");

echo "Testing autoloader...\n";

// Check if the file exists
$file_path = FS_SYS_CLASSES . 'data_importer.class.php';
echo "Looking for file: " . $file_path . "\n";
echo "File exists: " . (file_exists($file_path) ? 'YES' : 'NO') . "\n";

// Test loading the data_importer class
echo "Attempting to load system\\data_importer...\n";

try {
    // Try to use the class
    if (class_exists('system\\data_importer')) {
        echo "SUCCESS: Class exists and autoloader worked!\n";
        
        // Test creating a reflection to see if the method exists
        $reflection = new ReflectionClass('system\\data_importer');
        if ($reflection->hasMethod('process_json_data')) {
            echo "SUCCESS: process_json_data method exists!\n";
        } else {
            echo "ERROR: process_json_data method not found\n";
        }
    } else {
        echo "ERROR: Class does not exist\n";
    }
} catch (Error $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "EXCEPTION: " . $e->getMessage() . "\n";
}
