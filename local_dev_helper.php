<?php
/**
 * Local Development Helper
 * Quick commands for managing your local development environment
 */

function showUsage() {
    echo "Local Development Helper\n";
    echo "=======================\n\n";
    echo "Usage: php local_dev_helper.php [command]\n\n";
    echo "Commands:\n";
    echo "  setup     - Set up local database\n";
    echo "  sync      - Sync data from remote\n";
    echo "  export    - Export local changes\n";
    echo "  test      - Test database connections\n";
    echo "  status    - Show XAMPP status\n";
    echo "  start     - Start XAMPP services\n";
    echo "  phpinfo   - Show PHP configuration\n";
    echo "\n";
}

function testConnections() {
    echo "Testing Database Connections...\n\n";
    
    // Test local connection
    echo "Local Database (XAMPP):\n";
    try {
        $local_pdo = new PDO(
            'mysql:host=127.0.0.1;port=3306;charset=utf8',
            'root',
            '',
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        echo "  ✓ Connection successful\n";
        
        $version = $local_pdo->query('SELECT VERSION()')->fetchColumn();
        echo "  ✓ Version: $version\n";
        
        // Check if our database exists
        $stmt = $local_pdo->prepare('SHOW DATABASES LIKE ?');
        $stmt->execute(['autobooks_local']);
        if ($stmt->rowCount() > 0) {
            echo "  ✓ Database 'autobooks_local' exists\n";
        } else {
            echo "  ⚠️  Database 'autobooks_local' not found\n";
        }
        
    } catch (PDOException $e) {
        echo "  ❌ Connection failed: " . $e->getMessage() . "\n";
        echo "  💡 Make sure XAMPP MySQL is running\n";
    }
    
    echo "\nPHP Configuration:\n";
    echo "  ✓ PHP Version: " . PHP_VERSION . "\n";
    echo "  ✓ PDO MySQL: " . (extension_loaded('pdo_mysql') ? 'Available' : 'Not Available') . "\n";
    echo "  ✓ Current Directory: " . getcwd() . "\n";
}

function checkXamppStatus() {
    echo "XAMPP Status Check...\n\n";
    
    // Check if XAMPP processes are running
    $processes = [
        'httpd.exe' => 'Apache',
        'mysqld.exe' => 'MySQL'
    ];
    
    foreach ($processes as $process => $name) {
        $output = shell_exec("tasklist /FI \"IMAGENAME eq $process\" 2>NUL");
        if (strpos($output, $process) !== false) {
            echo "  ✓ $name is running\n";
        } else {
            echo "  ❌ $name is not running\n";
        }
    }
    
    // Check if ports are in use
    $ports = [
        '80' => 'Apache HTTP',
        '443' => 'Apache HTTPS', 
        '3306' => 'MySQL'
    ];
    
    echo "\nPort Status:\n";
    foreach ($ports as $port => $service) {
        $output = shell_exec("netstat -an | findstr :$port");
        if (!empty($output)) {
            echo "  ✓ Port $port ($service) is in use\n";
        } else {
            echo "  ⚠️  Port $port ($service) is not in use\n";
        }
    }
}

function startXampp() {
    echo "Starting XAMPP Services...\n\n";
    
    $xampp_path = 'E:\tools\xampp';
    
    if (!file_exists($xampp_path)) {
        echo "❌ XAMPP not found at $xampp_path\n";
        return;
    }
    
    echo "Starting Apache...\n";
    shell_exec("\"$xampp_path\\apache\\bin\\httpd.exe\" -k start");
    
    echo "Starting MySQL...\n";
    shell_exec("\"$xampp_path\\mysql\\bin\\mysqld.exe\" --defaults-file=\"$xampp_path\\mysql\\bin\\my.ini\" --standalone --console");
    
    echo "✓ Services started (check XAMPP Control Panel for status)\n";
}

function showPhpInfo() {
    echo "PHP Configuration Summary:\n";
    echo "========================\n\n";
    echo "PHP Version: " . PHP_VERSION . "\n";
    echo "PHP SAPI: " . PHP_SAPI . "\n";
    echo "Extensions:\n";
    
    $extensions = ['pdo', 'pdo_mysql', 'mysqli', 'curl', 'json', 'mbstring'];
    foreach ($extensions as $ext) {
        $status = extension_loaded($ext) ? '✓' : '❌';
        echo "  $status $ext\n";
    }
    
    echo "\nImportant Settings:\n";
    echo "  Memory Limit: " . ini_get('memory_limit') . "\n";
    echo "  Max Execution Time: " . ini_get('max_execution_time') . "s\n";
    echo "  Upload Max Filesize: " . ini_get('upload_max_filesize') . "\n";
    echo "  Post Max Size: " . ini_get('post_max_size') . "\n";
}

// Main execution
$command = $argv[1] ?? 'help';

switch ($command) {
    case 'setup':
        echo "Running database setup...\n";
        include 'local_db_setup.php';
        break;
        
    case 'sync':
        echo "Running database sync...\n";
        shell_exec('php db_sync.php pull');
        break;
        
    case 'export':
        echo "Exporting local changes...\n";
        shell_exec('php db_sync.php export');
        break;
        
    case 'test':
        testConnections();
        break;
        
    case 'status':
        checkXamppStatus();
        break;
        
    case 'start':
        startXampp();
        break;
        
    case 'phpinfo':
        showPhpInfo();
        break;
        
    case 'help':
    default:
        showUsage();
        break;
}
?>
