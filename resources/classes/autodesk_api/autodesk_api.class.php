<?php
namespace autodesk_api;


include_once(__DIR__ . '/config.php');
// $tokenEndpoint = '/v2/oauth/generateaccesstoken';

use system\data_importer;

class autodesk_api {
    public autodesk_products $products;
    public autodesk_quote $quote;
    public autodesk_quotes $quotes;
    public autodesk_order $order;
    public autodesk_orders $orders;
    public autodesk_subscription $subscription;
    public autodesk_subscriptions $subscriptions;
    public autodesk_authenticator $auth;
    public autodesk_customers $customers;
    public autodesk_customer_success $customer_success;
    public autodesk_api_interface $api;

    public bool $debug;

    public static $log_target;


    public function __construct($debug = false) {
        $this->api = new autodesk_api_interface();


        $this->debug = $debug;
        $this->auth            = new autodesk_authenticator($this->api);
        $this->products        = new autodesk_products($this->api);
        $this->customers       = new autodesk_customers($this->api);
        $this->order           = new autodesk_order($this->api, $this->products, $this->customers);
        $this->orders          = new autodesk_orders($this->api, $this->auth);
        $this->quote           = new autodesk_quote($this->api, $this->products, $this->customers);
        $this->quotes          = new autodesk_quotes($this->api, $this->auth);
        $this->subscription    = new autodesk_subscription($this->api);
        $this->subscriptions   = new autodesk_subscriptions($this->api, $this->auth);
        $this->customer_success = new autodesk_customer_success($this->api);
    }
    public static array $autodesk_history_table_schema = [
        //comment for reference
        'comment' => "  target, 
                        target_reference,
                        customer_csn,
                        date,
                        media,
                        message,
                        user_id,
                        email_address,
                        triggered_by,
                        result",
        "hist" => [
            'query' => "FROM autodesk_history hist"
        ],
        "users" => [
            'query' => "LEFT JOIN autobooks_users users ON hist.user_id = users.id"
        ]
    ];

    public static function database_set_storage(string $key, string $data): string {
        $query_sql = "INSERT INTO autodesk_storage (`autodesk_storage_key`, `autodesk_storage_data`)
                      VALUES (:autodesk_storage_key, :autodesk_storage_data)
                      ON DUPLICATE KEY UPDATE `autodesk_storage_data` = :autodesk_storage_data_update";
        try {
            $result = tep_db_affected_rows(tep_db_query(
                $query_sql,
                null,
                [
                    ":autodesk_storage_key" => $key,
                    ":autodesk_storage_data" => $data,
                    ":autodesk_storage_data_update" => $data
                ]
            ));
            return "success: " . $result;
        } catch (\Exception $e) {
            error_log("Failed to update storage for key '$key': " . $e->getMessage());
            return false;
        }
    }
    public static function get_unsubscribed(){
        return tcs_db_query("SELECT * FROM `autodesk_subscriptions_unsub`");
    }
    // Function to log messages
    public static function log_message(...$args):string {
        return tcs_log(...$args);
    }

    public static function database_get_storage(string $key): ?string {
        $query_sql = "SELECT `autodesk_storage_data` FROM autodesk_storage WHERE `autodesk_storage_key` = :autodesk_storage_key";

        try {
            $result = tep_db_fetch_array(tep_db_query($query_sql, null, [":autodesk_storage_key" => $key]));
            //print_rr($result);
            return $result['autodesk_storage_data'] ?? null;
        } catch (\Exception $e) {
            error_log("Failed to fetch storage for key '$key': " . $e->getMessage());
            return false;
        }
    }






    public static function update_history_from_api($json_payload): string|bool {
        $identifier = $json_payload['payload']['quoteNumber'] ?? $json_payload['payload']['subscriptionId'] ?? $json_payload['payload']['subscriptionReferenceNumber'] ?? null;
        if ($identifier == null) return false;
        $customer = autodesk_customer::get($identifier);
        $query_attribs = [
            ':customer_csn' => $customer['csn'],
            ':date' => date('Y-m-d', strtotime($json_payload['publishedAt'])),
            ':media' => 'autodesk_api',
            ':message' => $json_payload['payload']['message'],
            ':user_id' => 3,
            ':email_address' => 'autodesk_api',
            ':triggered_by' => 'autodesk_api',
            ':result' => 'autodesk_api'
        ];

        switch ($json_payload['topic']) {
            case 'subscription-change':
                $query_attribs[':target'] = 'subscription';
                $query_attribs[':target_reference'] = $json_payload['payload']['subscriptionId'];
            break;
            case 'quote-status':
                switch ($json_payload['event']) {
                    case 'created':
                    case 'changed':
                    case 'deleted':
                        $query_attribs[':target'] = 'quote';
                        $query_attribs[':target_reference'] = $json_payload['payload']['quoteNumber'];
                    break;
            }
                break;
            default:
                return false;
                break;
        }
        return self::database_update_history($query_attribs);
    }


    public static function database_update_history(array $attribs): ?array {
        print_rr($attribs, 'database_update_history');
        $query_attribs = [
            ':target' => $attribs['target'] ?? null,
            ':target_reference' => $attribs['target_reference'] ?? null,
            ':customer_csn' => $attribs['customer_csn'] ?? null,
            ':media' => $attribs['media'] ?? null,
            ':message' => $attribs['message'] ?? null,
            ':user_id' => $attribs['user_id'] ?? null,
            ':email_address' => $attribs['email_address'] ?? null,
            ':triggered_by' => $attribs['triggered_by'] ?? null,
            ':result' => $attribs['result'] ?? null
        ];

        $query_sql = "INSERT INTO `autodesk_history` (
            `id`,
            `target`,
            `target_reference`,
            `customer_csn`,
            `date`,
            `media`,
            `message`,
            `user_id`,
            `email_address`,
            `triggered_by`,
            `result`
        )
        VALUES (
            NULL,
            :target,
            :target_reference,
            :customer_csn,
            now(),
            :media,
            :message,
            :user_id,
            :email_address,
            :triggered_by,
            :result
        )";

        try {
            return tcs_db_query($query_sql, $query_attribs);
        } catch (\Exception $e) {
           tcs_log("Failed to update history: " . $e->getMessage(), 'autodesk_history');
            return false;
        }
    }
    private static function database_get_history($db_data = [], $criteria = [], $get_distinct = false)    {
        if (!is_array($criteria)) {
            throw new InvalidArgumentException('Expected $criteria to be an array.');
        }
        if (count($db_data) == 0) $db_data = [];

        $default_criteria = [ ];
        $query = build_select_query($db_data, self::$autodesk_history_table_schema, array_merge($default_criteria, $criteria), $get_distinct);

        print_rr(i: $query, l: 'finalhistq', fl: true);
        //  convert_column_names($query);
        return tcs_db_query($query['text']);
    }

    public static function get_history($target = false, $reference = false, $db_data = [], $criteria = [])    {
        print_rr($criteria, 'tivvity criteria');
        if(!$reference) return false;
        if(is_array($reference)){
            $criteria = [
                'where' => [
                     ['hist_target_reference','IN', $reference]
                ]
            ];
        } else {
            $criteria = [
                'where' => [
                    ['hist_target_reference','=', $reference]
                ]
            ];
        }
        if($target) $criteria['where']['hist_target'] = ['=', $target];
        return self::database_get_history($db_data, $criteria);
    }

    public static function get_product_catalog_header_map() {
        return [
            "products_autodesk_catalog" => [
                "table" => "products_autodesk_catalog",
                "key" => "autodeskId",
                "columns" => [
                    'autodeskId' => 'autodeskId',
                    'offeringName' => 'offeringName',
                    'offeringCode' => 'offeringCode',
                    'offeringId' => 'offeringId',
                    'intendedUsage.code' => 'intendedUsage_code',
                    'intendedUsage.description' => 'intendedUsage_description',
                    'accessModel.code' => 'accessModel_code',
                    'accessModel.description' => 'accessModel_description',
                    'servicePlan.code' => 'servicePlan_code',
                    'servicePlan.description' => 'servicePlan_description',
                    'connectivity.code' => 'connectivity_code',
                    'connectivity.description' => 'connectivity_description',
                    'term.code' => 'term_code',
                    'term.description' => 'term_description',
                    'lifeCycleState' => 'lifeCycleState',
                    'renewOnlyDate' => 'renewOnlyDate',
                    'discontinueDate' => 'discontinueDate',
                    'orderAction' => 'orderAction',
                    'specialProgramDiscount.code' => 'specialProgramDiscount_code',
                    'specialProgramDiscount.description' => 'specialProgramDiscount_description',
                    'fromQty' => 'fromQty',
                    'toQty' => 'toQty',
                    'currency' => 'currency',
                    'SRP' => 'SRP',
                    'costAfterSpecialProgramDiscount' => 'costAfterSpecialProgramDiscount',
                    'renewalDiscountPercent' => 'renewalDiscountPercent',
                    'renewalDiscountAmount' => 'renewalDiscountAmount',
                    'costAfterRenewalDiscount' => 'costAfterRenewalDiscount',
                    'transactionVolumeDiscountPercent' => 'transactionVolumeDiscountPercent',
                    'transactionVolumeDiscountAmount' => 'transactionVolumeDiscountAmount',
                    'costAfterTransactionVolumeDiscount' => 'costAfterTransactionVolumeDiscount',
                    'serviceDurationDiscountPercent' => 'serviceDurationDiscountPercent',
                    'serviceDurationDiscountAmount' => 'serviceDurationDiscountAmount',
                    'costAfterServiceDurationDiscount' => 'costAfterServiceDurationDiscount',
                    'effectiveStartDate' => 'effectiveStartDate',
                    'effectiveEndDate' => 'effectiveEndDate',
                ],
                "extra" => [
                    'unique_hash' => '<unique_hash>',
                    'hash_string' => '<hash_string>'
                ]

            ],
        ];
    }
    /**
     * Import CSV file into database tables based on mapping configuration
     * Delegates to DataImporter class
     *
     * @param array $mapping Mapping configuration for database tables and columns
     * @param string $csv_file_path Path to the CSV file
     * @param array $unique_hash Headers to use for generating unique hashes
     * @param bool $debug Whether to return detailed debug information
     * @return array Result information
     */
    public static function import_csv_into_database($mapping, $csv_file_path, $unique_hash = [], $debug = true):array {
        return \system\data_importer::import_csv_into_database($mapping, $csv_file_path, $unique_hash, $debug);
    }

    /**
     * Import data into database tables based on mapping configuration
     * Delegates to DataImporter class
     * Supports both CSV and JSON data sources
     *
     * @param array $mapping Mapping configuration for database tables and columns
     * @param string $file_path Path to the data file (CSV or JSON)
     * @param string $file_type Type of file ('csv' or 'json')
     * @param array $unique_hash Headers/keys to use for generating unique hashes
     * @param bool $debug Whether to return detailed debug information
     * @return array Result information
     */
    public static function import_data_into_database($mapping, $file_path, $file_type = 'csv', $unique_hash = [], $debug = true, $log_target = "main") {
        return data_importer::import_data_into_database($mapping, $file_path, $file_type, $unique_hash, $debug, $log_target);
    }

    /**
     * Process JSON data and import into database
     * Delegates to DataImporter class
     *
     * @param array $mapping Mapping configuration
     * @param array $json_data JSON data to process
     * @param array $unique_hash Keys to use for generating unique hashes
     * @param bool $debug Whether to return detailed debug information
     * @return array Result information
     */
    public static function process_json_data($mapping, $json_data, $unique_hash = [], $debug = true, $log_target = "main"): array
    {
        return data_importer::process_json_data($mapping, $json_data, $unique_hash, $debug, $log_target);
    }
}